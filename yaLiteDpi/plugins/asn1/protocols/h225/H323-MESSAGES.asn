-- H323-MESSAGES.asn
--
-- Minimal H.225.0 Call signalling for ASN.1 parsing
-- Based on ITU-T Recommendation H.225.0
-- Extremely simplified for asn1c compatibility - no external dependencies
--

-- Module H323-MESSAGES (H.225.0:12/2009)
H323-MESSAGES {itu-t(0) recommendation(0) h(8) h225-0(2250) version(0)
  7 h323-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- =======================================================================
-- Basic types and identifiers
-- =======================================================================

-- Object Identifier for protocol identification
ProtocolIdentifier ::= OBJECT IDENTIFIER

-- Basic string types
EndpointIdentifier ::= UTF8String(SIZE(1..128))

-- Basic identifiers
CallIdentifier ::= SEQUENCE {
  guid  GloballyUniqueID
}

ConferenceIdentifier ::= GloballyUniqueID

CallReferenceValue ::= INTEGER(0..65535)

GloballyUniqueID ::= OCTET STRING(SIZE(16))

-- =======================================================================
-- Non-standard parameter
-- =======================================================================
NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier  NonStandardIdentifier,
  data                   OCTET STRING
}

NonStandardIdentifier ::= CHOICE {
  object    OBJECT IDENTIFIER,
  h221NonStandard  H221NonStandard
}

H221NonStandard ::= SEQUENCE {
  t35CountryCode    INTEGER(0..255),
  t35Extension      INTEGER(0..255),
  manufacturerCode  INTEGER(0..65535)
}

-- =======================================================================
-- Endpoint type (minimal)
-- =======================================================================
EndpointType ::= SEQUENCE {
  nonStandardData                 NonStandardParameter OPTIONAL,
  mc                              BOOLEAN,
  undefinedNode                   BOOLEAN,
  ...
}

-- =======================================================================
-- H.323 User Information
-- =======================================================================
H323-UserInformation ::= SEQUENCE {
  h323-uu-pdu     H323-UU-PDU,
  user-data       SEQUENCE {
    protocol-discriminator  INTEGER(0..255),
    user-information        OCTET STRING(SIZE(1..131))
  } OPTIONAL,
  ...
}

-- =======================================================================
-- H323-UU-PDU and message types
-- =======================================================================
H323-UU-PDU ::= SEQUENCE {
  h323-message-body
    CHOICE {setup             Setup-UUIE,
            callProceeding    CallProceeding-UUIE,
            connect           Connect-UUIE,
            alerting          Alerting-UUIE,
            information       Information-UUIE,
            releaseComplete   ReleaseComplete-UUIE,
            facility          Facility-UUIE,
            ...,
            progress          Progress-UUIE,
            empty             NULL,
            status            Status-UUIE,
            statusInquiry     StatusInquiry-UUIE,
            setupAcknowledge  SetupAcknowledge-UUIE,
            notify            Notify-UUIE},
  nonStandardData                     NonStandardParameter OPTIONAL,
  ...,
  h4501SupplementaryService           SEQUENCE OF OCTET STRING OPTIONAL,
  h245Tunnelling                      BOOLEAN,
  h245Control                         SEQUENCE OF OCTET STRING OPTIONAL,
  nonStandardControl                  SEQUENCE OF NonStandardParameter OPTIONAL
}

-- =======================================================================
-- Message type definitions (minimal)
-- =======================================================================
Setup-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  sourceInfo                  EndpointType,
  conferenceID                ConferenceIdentifier,
  callIdentifier              CallIdentifier,
  activeMC                    BOOLEAN,
  mediaWaitForConnect         BOOLEAN,
  canOverlapSend              BOOLEAN,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

CallProceeding-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  destinationInfo             EndpointType,
  callIdentifier              CallIdentifier,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

Connect-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  destinationInfo             EndpointType,
  conferenceID                ConferenceIdentifier,
  callIdentifier              CallIdentifier,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

Alerting-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  destinationInfo             EndpointType,
  callIdentifier              CallIdentifier,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

Information-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

ReleaseComplete-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

Facility-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  reason                      FacilityReason,
  callIdentifier              CallIdentifier,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

Progress-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  destinationInfo             EndpointType,
  callIdentifier              CallIdentifier,
  multipleCalls               BOOLEAN,
  maintainConnection          BOOLEAN,
  ...
}

Status-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

StatusInquiry-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

SetupAcknowledge-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

Notify-UUIE ::= SEQUENCE {
  protocolIdentifier          ProtocolIdentifier,
  callIdentifier              CallIdentifier,
  ...
}

-- =======================================================================
-- Supporting type definitions (minimal)
-- =======================================================================
FacilityReason ::= CHOICE {
  routeCallToGatekeeper       NULL,
  callForwarded               NULL,
  routeCallToMC               NULL,
  undefinedReason             NULL,
  conferenceListChoice        NULL,
  startH245                   NULL,
  noH245                      NULL,
  newTokens                   NULL,
  featureSetUpdate            NULL,
  forwardedElements           NULL,
  transportedInformation      NULL,
  ...
}

END
